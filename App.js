import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, View, Text, Image, ScrollView, TouchableOpacity, Dimensions, FlatList } from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';

// Import assets directly
const assets = {
  logo: require('./assets/pama-logo.svg'),
  instagram: require('./assets/instagram-icon.svg'),
  attendance: require('./assets/attendance.svg'),
  atr: require('./assets/atr.svg'),
  cnm: require('./assets/cnm.svg'),
  newpoints: require('./assets/newpoints.svg'),
  sap: require('./assets/sap.svg'),
  ipeak: require('./assets/ipeak.svg'),
  viewAll: require('./assets/view-all.svg'),
};

const SLIDER_WIDTH = Dimensions.get('window').width;

const carouselItems = [
  {
    title: 'JUST LAUNCHED!',
    subtitle: '@pamapersadaofficial',
    description: 'Go follow and get our latest news, trends & activities in a more fun & fresh content!',
    image: 'https://via.placeholder.com/800x400/FFD700/000000?text=JUST+LAUNCHED',
    fallbackImage: 'https://via.placeholder.com/800x400/E0E0E0/666666?text=Image+Not+Available'
  },
  {
    title: '📢 Town Hall Q3 2025',
    subtitle: '15 Okt 2025 - Auditorium',
    description: 'Join us for quarterly updates and company announcements',
    image: 'https://via.placeholder.com/800x400/4285F4/FFFFFF?text=Town+Hall+Q3',
    fallbackImage: 'https://via.placeholder.com/800x400/E0E0E0/666666?text=Image+Not+Available'
  },
];

const menuItems = [
  { id: 1, icon: assets.attendance, title: 'Attendance\nRecording', color: '#4285F4' },
  { id: 2, icon: assets.atr, title: 'ATR\nPribadi', color: '#4285F4' },
  { id: 3, icon: assets.cnm, title: 'CNM', color: '#4285F4' },
  { id: 4, icon: assets.newpoints, title: 'New Points', color: '#4285F4' },
  { id: 5, icon: assets.sap, title: 'SAP', color: '#4285F4' },
  { id: 6, icon: assets.ipeak, title: 'iPeak', color: '#4285F4' },
  { id: 7, icon: assets.viewAll, title: 'Lihat\nSemua', color: '#4285F4' },
];

const activities = [
  {
    id: 1,
    image: 'https://via.placeholder.com/200x200/4285F4/FFFFFF?text=Director',
    title: 'Message from Director\'s Desk',
    fallbackImage: 'https://via.placeholder.com/200x200/E0E0E0/666666?text=No+Image'
  },
  {
    id: 2,
    image: 'https://via.placeholder.com/200x200/4285F4/FFFFFF?text=KE',
    title: 'KE',
    fallbackImage: 'https://via.placeholder.com/200x200/E0E0E0/666666?text=No+Image'
  },
];

const navItems = [
  { id: 'home', icon: 'home-outline', label: 'Home' },
  { id: 'menu', icon: 'menu', label: 'Menu' },
  { id: 'add', icon: 'person-add', label: '' },
  { id: 'notifications', icon: 'notifications-outline', label: 'Notifications', badge: true },
  { id: 'person', icon: 'person-outline', label: 'Profile' },
];

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
          <MaterialIcons name="error-outline" size={48} color="#FF3B30" />
          <Text style={{ fontSize: 18, fontWeight: 'bold', marginTop: 16, textAlign: 'center' }}>
            Oops! Something went wrong
          </Text>
          <Text style={{ fontSize: 14, color: '#666', marginTop: 8, textAlign: 'center' }}>
            Please restart the app or contact support if the problem persists.
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: '#2196F3',
              paddingHorizontal: 20,
              paddingVertical: 10,
              borderRadius: 8,
              marginTop: 20
            }}
            onPress={() => this.setState({ hasError: false, error: null })}
          >
            <Text style={{ color: '#FFF', fontWeight: '600' }}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

// Component untuk Image dengan error handling dan loading state
const SafeImage = ({ source, fallbackSource, style, ...props }) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  return (
    <View style={style}>
      {isLoading && (
        <View style={[
          style,
          {
            position: 'absolute',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#F0F0F0'
          }
        ]}>
          <MaterialIcons name="image" size={24} color="#CCC" />
        </View>
      )}
      <Image
        source={imageError ? { uri: fallbackSource } : source}
        style={style}
        onError={() => {
          setImageError(true);
          setIsLoading(false);
        }}
        onLoad={() => setIsLoading(false)}
        onLoadStart={() => setIsLoading(true)}
        {...props}
      />
    </View>
  );
};

// Component untuk SVG Icon dengan fallback
const SafeSvgIcon = ({ source, style, fallback = '📱' }) => {
  const [svgError, setSvgError] = useState(false);

  if (svgError) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ fontSize: 24 }}>{fallback}</Text>
      </View>
    );
  }

  try {
    return <Image source={source} style={style} onError={() => setSvgError(true)} />;
  } catch (error) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ fontSize: 24 }}>{fallback}</Text>
      </View>
    );
  }
};

export default function App() {
  const carouselRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const intervalRef = useRef(null);

  // Auto-scroll carousel dengan pause saat user berinteraksi
  useEffect(() => {
    if (!isUserInteracting) {
      intervalRef.current = setInterval(() => {
        setActiveIndex(prevIndex => {
          const nextIndex = prevIndex < carouselItems.length - 1 ? prevIndex + 1 : 0;

          if (carouselRef.current) {
            try {
              carouselRef.current.scrollToIndex({
                index: nextIndex,
                animated: true
              });
            } catch (error) {
              console.warn('Carousel scroll error:', error);
            }
          }

          return nextIndex;
        });
      }, 3000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isUserInteracting]);

  // Pause auto-scroll saat user berinteraksi
  const handleUserInteraction = () => {
    setIsUserInteracting(true);
    setTimeout(() => setIsUserInteracting(false), 5000); // Resume setelah 5 detik
  };
  const renderCarouselItem = ({ item }) => (
    <View style={styles.carouselItem}>
      <SafeImage
        source={{ uri: item.image }}
        fallbackSource={item.fallbackImage}
        style={styles.carouselImage}
      />
      <View style={styles.carouselContent}>
        <Text style={styles.carouselTitle}>{item.title}</Text>
        <View style={styles.searchContainer}>
          <Text style={styles.carouselSubtitle}>{item.subtitle}</Text>
          <MaterialIcons name="search" size={24} color="#000" style={styles.searchIcon} />
        </View>
        <Text style={styles.carouselDescription}>{item.description}</Text>
        <SafeSvgIcon
          source={assets.instagram}
          style={styles.instagramIcon}
          fallback="📷"
        />
      </View>
    </View>
  );

  const [pressedItem, setPressedItem] = React.useState(null);
  const [activeTab, setActiveTab] = React.useState('home');

  const renderMenuItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.menuItem, pressedItem === item.id && styles.menuItemPressed]}
      onPressIn={() => setPressedItem(item.id)}
      onPressOut={() => setPressedItem(null)}
      activeOpacity={0.7}
    >
      <View 
        style={[
          styles.menuIcon,
          { backgroundColor: item.color },
          pressedItem === item.id && styles.menuIconPressed
        ]}
      >
        <SafeSvgIcon
          source={item.icon}
          style={styles.menuIconImage}
          fallback="📱"
        />
      </View>
      <Text 
        style={[
          styles.menuTitle,
          pressedItem === item.id && styles.menuTitlePressed
        ]}
      >
        {item.title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <SafeAreaView style={styles.container}>
          <StatusBar style="auto" />
        
        {/* Header */}
      <View style={styles.header}>
        <SafeSvgIcon
          source={assets.logo}
          style={styles.logo}
          fallback="🏢"
        />
        <Text style={styles.headerText}>EVENTS | ACTIVITIES | NEWS | UPDATES</Text>
        <View style={styles.headerRight}>
          <View style={styles.dotContainer}>
            <View style={styles.dot} />
            <View style={styles.dot} />
            <View style={styles.dot} />
          </View>
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Carousel */}
        <View style={styles.carouselContainer}>
          <FlatList
            ref={carouselRef}
            data={carouselItems}
            renderItem={renderCarouselItem}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            keyExtractor={(_, index) => index.toString()}
            snapToInterval={SLIDER_WIDTH}
            decelerationRate="fast"
            onScrollBeginDrag={handleUserInteraction}
            onMomentumScrollEnd={(event) => {
              const newIndex = Math.round(
                event.nativeEvent.contentOffset.x / SLIDER_WIDTH
              );
              setActiveIndex(newIndex);
            }}
            onScrollToIndexFailed={(info) => {
              console.warn('Scroll to index failed:', info);
              // Fallback: scroll to a safe index
              setTimeout(() => {
                if (carouselRef.current) {
                  carouselRef.current.scrollToOffset({
                    offset: info.index * SLIDER_WIDTH,
                    animated: true
                  });
                }
              }, 100);
            }}
          />
          <View style={styles.paginationDots}>
            {carouselItems.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === activeIndex && styles.activeDot
                ]}
              />
            ))}
          </View>
        </View>

        {/* Menu Tabs */}
        <View style={styles.tabContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity style={[styles.tab, styles.activeTab]}>
              <Text style={[styles.tabText, styles.activeTabText]}>Favorit</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.tab}>
              <Text style={styles.tabText}>Terbaru</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.tab}>
              <Text style={styles.tabText}>Sering Dilihat</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Menu Grid */}
        <View style={styles.menuGrid}>
          {menuItems.map(renderMenuItem)}
        </View>

        {/* Video Populer */}
        <View style={styles.videoSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Video populer</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <MaterialIcons name="play-circle-outline" size={24} color="#007AFF" />
              <Text style={styles.viewAllText}>Lihat Semua</Text>
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {activities.map(activity => (
              <TouchableOpacity key={activity.id} style={styles.videoItem}>
                <View style={styles.videoThumbnail}>
                  <SafeImage
                    source={{ uri: activity.image }}
                    fallbackSource={activity.fallbackImage}
                    style={styles.videoImage}
                  />
                  <View style={styles.playButton}>
                    <MaterialIcons name="play-circle-filled" size={40} color="#FFF" />
                  </View>
                </View>
                <Text style={styles.videoTitle}>{activity.title}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        {navItems.map(item => (
          <TouchableOpacity
            key={item.id}
            style={[styles.navItem, item.id === activeTab && styles.activeNavItem]}
            onPress={() => setActiveTab(item.id)}
          >
            {item.id === 'add' ? (
              <View style={styles.fabButton}>
                <MaterialIcons name={item.icon} size={24} color="#FFF" />
              </View>
            ) : (
              <View style={styles.navItemContent}>
                {item.badge && <View style={styles.badge} />}
                <MaterialIcons
                  name={item.icon}
                  size={24}
                  color={item.id === activeTab ? '#007AFF' : '#666'}
                />
                <Text
                  style={[
                    styles.navLabel,
                    item.id === activeTab && styles.activeNavLabel
                  ]}
                >
                  {item.label}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
        </SafeAreaView>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  // Container & Layout
  container: {
    flex: 1,
    backgroundColor: '#F5F6FA',
    width: '100%',
  },

  // Header Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  logo: {
    width: 40,
    height: 40,
    resizeMode: 'contain'
  },
  headerText: {
    fontSize: 12,
    color: '#666',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 10
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  dotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3
  },

  // Dot Styles (unified)
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(255,255,255,0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#FFF',
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Carousel Styles
  carouselContainer: {
    width: '100%',
  },
  carouselItem: {
    width: SLIDER_WIDTH,
    height: 200,
    position: 'relative',
  },
  carouselImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  carouselContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    paddingTop: 40,
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  carouselTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
    marginBottom: 4,
  },
  carouselSubtitle: {
    fontSize: 14,
    color: '#FFF',
    opacity: 0.8,
    marginBottom: 8,
  },
  carouselDescription: {
    fontSize: 14,
    color: '#FFF',
    marginBottom: 15,
    lineHeight: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  searchIcon: {
    marginLeft: 10,
  },
  instagramIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  paginationDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 70,
    left: 0,
    right: 0,
    zIndex: 1,
  },

  // Tab Styles
  tabContainer: {
    backgroundColor: '#FFF',
    paddingVertical: 12,
    marginBottom: 8,
  },
  tab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
  },
  activeTab: {
    backgroundColor: '#2196F3',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#FFF',
    fontWeight: '600',
  },
    color: '#FFF',
    marginTop: 4,
    opacity: 0.8,
  },

  // Menu Styles
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    backgroundColor: '#FFF',
    borderRadius: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  menuItem: {
    width: '25%',
    alignItems: 'center',
    marginVertical: 12,
    paddingHorizontal: 8,
    transform: [{ scale: 1 }],
  },
  menuItemPressed: {
    transform: [{ scale: 0.95 }],
  },
  menuIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    transform: [{ scale: 1 }],
  },
  menuIconPressed: {
    transform: [{ scale: 0.95 }],
    shadowOpacity: 0.05,
    elevation: 1,
  },
  menuIconImage: {
    width: '100%',
    height: '100%',
  },
  menuTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
    opacity: 1,
  },
  menuTitlePressed: {
    opacity: 0.8,
  },

  // Video Section Styles
  videoSection: {
    backgroundColor: '#FFF',
    padding: 16,
    marginBottom: 80,
    marginHorizontal: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    color: '#2196F3',
    marginLeft: 5,
    fontSize: 14,
    fontWeight: '600',
  },
  videoItem: {
    width: 160,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  videoThumbnail: {
    position: 'relative',
    width: '100%',
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 8,
  },
  videoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    resizeMode: 'cover',
  },
  videoTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginTop: 8,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
  },

  // Bottom Navigation Styles
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#FFF',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEE',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    borderRadius: 8,
  },
  activeNavItem: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  navItemContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  navLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontWeight: '500',
  },
  activeNavLabel: {
    color: '#2196F3',
    fontWeight: '600',
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF3B30',
    zIndex: 1,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -28,
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 3,
    borderColor: '#FFF',
  },
});