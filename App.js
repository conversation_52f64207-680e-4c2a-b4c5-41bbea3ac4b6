import React, { useRef, useEffect } from 'react';
import { StyleSheet, View, Text, Image, ScrollView, TouchableOpacity, Dimensions, FlatList, TextInput } from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Feather, MaterialIcons, Ionicons } from '@expo/vector-icons';
import { SvgUri } from 'react-native-svg';

const SLIDER_WIDTH = Dimensions.get('window').width;
const ITEM_WIDTH = Math.round(SLIDER_WIDTH * 0.9);

const carouselItems = [
  {
    title: 'JUST LAUNCHED!',
    subtitle: '@pamapersadaofficial',
    description: 'Go follow and get our latest news, trends & activities in a more fun & fresh content!',
    image: 'https://via.placeholder.com/800x400/FFD700/FFFFFF'
  },
  {
    title: '📢 Town Hall Q3 2025',
    subtitle: '15 Okt 2025 - Auditorium',
    description: '',
    image: 'https://via.placeholder.com/800x400'
  },
];

const menuItems = [
  { id: 1, icon: 'attendance.svg', title: 'Attendance\nRecording', color: '#4285F4' },
  { id: 2, icon: 'atr.svg', title: 'ATR\nPribadi', color: '#4285F4' },
  { id: 3, icon: 'cnm.svg', title: 'CNM', color: '#4285F4' },
  { id: 4, icon: 'newpoints.svg', title: 'New Points', color: '#4285F4' },
  { id: 5, icon: 'sap.svg', title: 'SAP', color: '#4285F4' },
  { id: 6, icon: 'ipeak.svg', title: 'iPeak', color: '#4285F4' },
  { id: 7, icon: 'view-all.svg', title: 'Lihat\nSemua', color: '#4285F4' },
];

const activities = [
  { id: 1, image: 'https://via.placeholder.com/200x200', title: 'Message from Director\'s Desk' },
  { id: 2, image: 'https://via.placeholder.com/200x200', title: 'KE' },
];

const navItems = [
  { id: 'home', icon: 'home-outline', label: 'Home' },
  { id: 'menu', icon: 'menu', label: 'Menu' },
  { id: 'add', icon: 'person-add', label: '' },
  { id: 'notifications', icon: 'notifications-outline', label: 'Notifications', badge: true },
  { id: 'person', icon: 'person-outline', label: 'Profile' },
];

export default function App() {
  const carouselRef = useRef(null);
  const [activeIndex, setActiveIndex] = React.useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      if (carouselRef.current && activeIndex < carouselItems.length - 1) {
        setActiveIndex(activeIndex + 1);
        carouselRef.current.scrollToIndex({
          index: activeIndex + 1,
          animated: true
        });
      } else {
        setActiveIndex(0);
        carouselRef.current.scrollToIndex({
          index: 0,
          animated: true
        });
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [activeIndex]);
  const renderCarouselItem = ({ item }) => (
    <View style={styles.carouselItem}>
      <Image source={{ uri: item.image }} style={styles.carouselImage} />
      <View style={styles.carouselContent}>
        <Text style={styles.carouselTitle}>{item.title}</Text>
        <View style={styles.searchContainer}>
          <Text style={styles.carouselSubtitle}>{item.subtitle}</Text>
          <MaterialIcons name="search" size={24} color="#000" style={styles.searchIcon} />
        </View>
        <Text style={styles.carouselDescription}>{item.description}</Text>
        <SvgUri
          uri={`file://${__dirname}/assets/instagram-icon.svg`}
          style={styles.instagramIcon}
        />
      </View>
    </View>
  );

  const [pressedItem, setPressedItem] = React.useState(null);
  const [activeTab, setActiveTab] = React.useState('home');

  const renderMenuItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.menuItem, pressedItem === item.id && styles.menuItemPressed]}
      onPressIn={() => setPressedItem(item.id)}
      onPressOut={() => setPressedItem(null)}
      activeOpacity={0.7}
    >
      <View 
        style={[
          styles.menuIcon,
          { backgroundColor: item.color },
          pressedItem === item.id && styles.menuIconPressed
        ]}
      >
        <SvgUri
          uri={`file://${__dirname}/assets/${item.icon}`}
          style={styles.menuIconImage}
        />
      </View>
      <Text 
        style={[
          styles.menuTitle,
          pressedItem === item.id && styles.menuTitlePressed
        ]}
      >
        {item.title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        
        {/* Header */}
      <View style={styles.header}>
        <SvgUri
          uri={`file://${__dirname}/assets/pama-logo.svg`}
          style={styles.logo}
        />
        <Text style={styles.headerText}>EVENTS | ACTIVITIES | NEWS | UPDATES</Text>
        <View style={styles.headerRight}>
          <View style={styles.dotContainer}>
            <View style={styles.dot} />
            <View style={styles.dot} />
            <View style={styles.dot} />
          </View>
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Carousel */}
        <View style={styles.carouselContainer}>
          <FlatList
            ref={carouselRef}
            data={carouselItems}
            renderItem={renderCarouselItem}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            snapToInterval={SLIDER_WIDTH}
            decelerationRate="fast"
            onMomentumScrollEnd={(event) => {
              const newIndex = Math.round(
                event.nativeEvent.contentOffset.x / SLIDER_WIDTH
              );
              setActiveIndex(newIndex);
            }}
          />
          <View style={styles.paginationDots}>
            {carouselItems.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === activeIndex && styles.activeDot
                ]}
              />
            ))}
          </View>
        </View>

        {/* Menu Tabs */}
        <View style={styles.tabContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity style={[styles.tab, styles.activeTab]}>
              <Text style={[styles.tabText, styles.activeTabText]}>Favorit</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.tab}>
              <Text style={styles.tabText}>Terbaru</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.tab}>
              <Text style={styles.tabText}>Sering Dilihat</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Menu Grid */}
        <View style={styles.menuGrid}>
          {menuItems.map(renderMenuItem)}
        </View>

        {/* Video Populer */}
        <View style={styles.videoSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Video populer</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <MaterialIcons name="play-circle-outline" size={24} color="#007AFF" />
              <Text style={styles.viewAllText}>Lihat Semua</Text>
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.videoContainer}>
            {activities.map(activity => (
              <TouchableOpacity key={activity.id} style={styles.videoItem}>
                <View style={styles.videoThumbnail}>
                  <Image source={{ uri: activity.image }} style={styles.videoImage} />
                  <View style={styles.playButton}>
                    <MaterialIcons name="play-circle-filled" size={40} color="#FFF" />
                  </View>
                </View>
                <Text style={styles.videoTitle}>{activity.title}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        {navItems.map(item => (
          <TouchableOpacity
            key={item.id}
            style={[styles.navItem, item.id === activeTab && styles.activeNavItem]}
            onPress={() => setActiveTab(item.id)}
          >
            {item.id === 'add' ? (
              <View style={styles.fabButton}>
                <MaterialIcons name={item.icon} size={24} color="#FFF" />
              </View>
            ) : (
              <View style={styles.navItemContent}>
                {item.badge && <View style={styles.badge} />}
                <MaterialIcons
                  name={item.icon}
                  size={24}
                  color={item.id === activeTab ? '#007AFF' : '#666'}
                />
                <Text
                  style={[
                    styles.navLabel,
                    item.id === activeTab && styles.activeNavLabel
                  ]}
                >
                  {item.label}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee'
  },
  logo: {
    width: 40,
    height: 40,
    resizeMode: 'contain'
  },
  headerText: {
    fontSize: 12,
    color: '#666',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 10
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  dotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#666'
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  searchIcon: {
    marginLeft: 10,
  },
  carouselDescription: {
    fontSize: 14,
    color: '#000',
    marginBottom: 15,
    lineHeight: 20,
  },
  instagramIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  paginationDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 70,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(255,255,255,0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#FFF',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  container: {
    flex: 1,
    backgroundColor: '#F5F6FA',
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  profileContainer: {
    width: 42,
    height: 42,
    borderRadius: 21,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#2196F3',
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  logoutButton: {
    padding: 8,
    backgroundColor: '#F5F6FA',
    borderRadius: 8,
  },
  carouselContainer: {
    width: '100%',
  },
  carouselItem: {
    width: SLIDER_WIDTH,
    height: 200,
    position: 'relative',
  },
  carouselImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  carouselContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 20,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    paddingTop: 40,
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  carouselTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
  },
  carouselSubtitle: {
    fontSize: 18,
    color: '#000',
    fontWeight: '500',
    fontSize: 14,
    color: '#FFF',
    marginTop: 4,
    opacity: 0.8,
  },
  tabContainer: {
    backgroundColor: '#FFF',
    paddingVertical: 12,
    marginBottom: 8,
  },
  tab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
  },
  activeTab: {
    backgroundColor: '#2196F3',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#FFF',
    fontWeight: '600',
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    backgroundColor: '#FFF',
    borderRadius: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  menuItem: {
    width: '25%',
    alignItems: 'center',
    marginVertical: 12,
    paddingHorizontal: 8,
    transform: [{ scale: 1 }],
  },
  menuItemPressed: {
    transform: [{ scale: 0.95 }],
  },
  menuIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    transform: [{ scale: 1 }],
  },
  menuIconPressed: {
    transform: [{ scale: 0.95 }],
    shadowOpacity: 0.05,
    elevation: 1,
  },
  menuIconImage: {
     width: '100%',
     height: '100%',
   },
   menuIcon: {
     width: 56,
     height: 56,
     borderRadius: 28,
     justifyContent: 'center',
     alignItems: 'center',
     marginBottom: 8,
     shadowColor: '#000',
     shadowOffset: { width: 0, height: 2 },
     shadowOpacity: 0.1,
     shadowRadius: 4,
     elevation: 2,
     transform: [{ scale: 1 }],
   },
  menuTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
    opacity: 1,
  },
  menuTitlePressed: {
    opacity: 0.8,
  },
  videoSection: {
    paddingHorizontal: 15,
    marginTop: 20,
    backgroundColor: '#FFF',
    padding: 16,
    marginBottom: 80,
    marginHorizontal: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    color: '#007AFF',
    marginLeft: 5,
    fontSize: 14,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '600',
  },
  videoItem: {
    width: 160,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  videoThumbnail: {
    position: 'relative',
    width: '100%',
    height: 160,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 8,
  },
  videoImage: {
    width: 160,
    height: 160,
    borderRadius: 12,
    resizeMode: 'cover',
  },
  videoTitle: {
    fontSize: 14,
    color: '#333',
    marginTop: 5,
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginTop: 8,
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#FFF',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEE',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    paddingVertical: 4,
    borderRadius: 8,
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF3B30',
    zIndex: 1,
  },
  activeNavItem: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  navItemContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  navLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontWeight: '500',
  },
  activeNavLabel: {
    color: '#007AFF',
    color: '#2196F3',
    fontWeight: '600',
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -28,
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 3,
    borderColor: '#FFF',
  },
});