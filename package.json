{"name": "psg-bmi", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@expo/vector-icons": "^13.0.0", "@expo/webpack-config": "^19.0.0", "expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.10", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "^15.12.0", "react-native-web": "~0.19.6"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}