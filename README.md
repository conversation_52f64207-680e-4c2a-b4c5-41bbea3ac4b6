# Portal Karyawan Mobile App

Aplikasi portal karyawan mobile dengan desain modern bergaya mBanking. Dibangun menggunakan React Native dan Expo.

## Fitur Utama

- 🔒 Autentikasi pengguna dengan foto profil
- 📱 Antarmuka modern bergaya mBanking
- 🎯 Menu aplikasi dengan grid layout
- 🔄 Carousel banner informasi
- 📸 Galeri aktivitas perusahaan
- 📱 Navigasi bottom bar dengan FAB

## Teknologi

- React Native
- Expo
- React Native Snap Carousel
- React Native Safe Area Context
- Expo Vector Icons

## Instalasi

1. Pastikan Node.js dan npm terinstal di sistem Anda
2. Install Expo CLI secara global:
   ```bash
   npm install -g expo-cli
   ```
3. Clone repositori ini
4. Masuk ke direktori proyek:
   ```bash
   cd psg-bmi
   ```
5. Install dependensi:
   ```bash
   npm install
   ```
6. Jalankan aplikasi:
   ```bash
   npm start
   ```

## Struktur Menu

- Attendance Recording 🕒
  - Informasi absensi bulanan
  - Status kehadiran
  - Pengajuan izin/cuti

- ATR Pribadi 🧾
  - Pengajuan kerja
  - Dinas pribadi

- SAP 🗂
  - Modul SAP SHE
  - Greencard
  - IUT & OTT

- Production 📊
  - Data produksi harian
  - Grafik & statistik

- Profil Karyawan 👤
  - Informasi personal
  - Data pekerjaan

- Raport Operator 📋
  - Performa operator
  - Evaluasi unit

- iPeak 🌟
  - Penilaian karyawan
  - Kompetensi

## Desain UI

- Warna: Dominan putih dengan aksen biru
- Font: Modern (Inter, SF Pro, Roboto)
- Ikon: Feather Icons
- Elemen visual:
  - Shadow lembut
  - Sudut membulat
  - Animasi transisi

## Pengembangan

Proyek ini menggunakan Expo untuk mempermudah pengembangan aplikasi React Native. Untuk berkontribusi:

1. Fork repositori
2. Buat branch fitur baru
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request